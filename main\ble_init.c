#include "ble_init.h"
#include "esp_log.h"
#include "esp_bt.h"
#include "esp_bt_main.h"
#include "ble_gap.h"
#include "ble_gatt.h"

static const char *TAG = "BLE_INIT";

esp_err_t ble_init(void)
{
    esp_err_t ret;
    
    ESP_LOGI(TAG, "Starting BLE initialization...");
    
    // 使用BLE与主从通讯
    // 此函数将BSS、数据和控制器的其他部分释放到堆中。总大小约为70KB
    // 如果您只打算使用BLE，调用esp_bt_controller_mem_release（esp_bt_MODE_CLASSIC_bt）可以释放BSS和经典蓝牙控制器消耗的数据。
    ret = esp_bt_controller_mem_release(ESP_BT_MODE_CLASSIC_BT);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Release classic BT memory failed: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "Classic BT memory released");

    // 使用官方默认的参数去初始化蓝牙底层的硬件资源，固定写法，不用深究
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    ret = esp_bt_controller_init(&bt_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Initialize BT controller failed: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "BT controller initialized");

    // 控制器初始化完以后，还要显式"启用"，否则不会工作, 固定写法, 不用深究
    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Enable BT controller failed: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "BT controller enabled");

    // 初始化蓝牙协议栈
    // 控制器负责底层收发，协议栈负责 BLE 逻辑处理，比如 GATT 等  固定写法, 不用深究
    ret = esp_bluedroid_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Initialize Bluedroid failed: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "Bluedroid initialized");

    // 使能蓝牙协议栈
    // 初始化只是分配资源，启动才是真正开始运行  固定写法, 不用深究
    ret = esp_bluedroid_enable();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Enable Bluedroid failed: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "Bluedroid enabled");

    // 初始化 GAP 层
    ret = ble_gap_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "GAP init failed: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "GAP layer initialized");

    // 初始化 GATT 层
    ret = ble_gatt_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "GATT init failed: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "GATT layer initialized");
    
    ESP_LOGI(TAG, "BLE initialization completed successfully");
    return ESP_OK;
}
