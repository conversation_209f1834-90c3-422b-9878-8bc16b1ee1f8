#include "dev_led.h"
#include "esp_log.h"

static const char *TAG = "DEV_LED";
static bool led_initialized = false;
static led_state_t current_led_state = LED_STATE_OFF;
static TaskHandle_t blink_task_handle = NULL;
static uint32_t blink_interval = 1000; // 默认1秒

/**
 * @brief LED闪烁任务函数
 */
static void led_blink_task(void *pvParameters)
{
    while (1) {
        // 切换LED状态
        dev_led_toggle();

        ESP_LOGI(TAG, "LED %s", (current_led_state == LED_STATE_ON) ? "ON" : "OFF");

        // 延时
        vTaskDelay(pdMS_TO_TICKS(blink_interval));
    }
}

esp_err_t dev_led_init(void)
{
    if (led_initialized) {
        ESP_LOGW(TAG, "LED already initialized");
        return ESP_OK;
    }

    // 配置GPIO为输出模式
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,     // 禁用中断
        .mode = GPIO_MODE_OUTPUT,           // 输出模式
        .pin_bit_mask = (1ULL << LED_GPIO), // LED_GPIO
        .pull_down_en = 0,                  // 禁用下拉
        .pull_up_en = 0,                    // 禁用上拉
    };

    esp_err_t ret = gpio_config(&io_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "GPIO config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 初始状态设为低电平（LED关闭）
    ret = dev_led_set_state(LED_STATE_OFF);
    if (ret != ESP_OK) {
        return ret;
    }

    led_initialized = true;
    ESP_LOGI(TAG, "GPIO%d initialized as LED output", LED_GPIO);

    return ESP_OK;
}

esp_err_t dev_led_set_state(led_state_t state)
{
    if (!led_initialized) {
        ESP_LOGE(TAG, "LED not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (state != LED_STATE_OFF && state != LED_STATE_ON) {
        ESP_LOGE(TAG, "Invalid LED state: %d", state);
        return ESP_ERR_INVALID_ARG;
    }

    esp_err_t ret = gpio_set_level(LED_GPIO, state);
    if (ret == ESP_OK) {
        current_led_state = state;
    }

    return ret;
}

led_state_t dev_led_get_state(void)
{
    return current_led_state;
}

esp_err_t dev_led_toggle(void)
{
    if (!led_initialized) {
        ESP_LOGE(TAG, "LED not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    led_state_t new_state = (current_led_state == LED_STATE_ON) ? LED_STATE_OFF : LED_STATE_ON;
    return dev_led_set_state(new_state);
}

esp_err_t dev_led_start_blink_task(uint32_t blink_interval_ms, uint8_t task_priority)
{
    if (!led_initialized) {
        ESP_LOGE(TAG, "LED not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (blink_task_handle != NULL) {
        ESP_LOGW(TAG, "Blink task already running");
        return ESP_OK;
    }

    blink_interval = blink_interval_ms;

    BaseType_t task_ret = xTaskCreate(
        led_blink_task,         // 任务函数
        "led_blink",            // 任务名称
        2048,                   // 堆栈大小
        NULL,                   // 任务参数
        task_priority,          // 任务优先级
        &blink_task_handle      // 任务句柄
    );

    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create LED blink task");
        blink_task_handle = NULL;
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "LED blink task started with %lu ms interval", blink_interval);
    return ESP_OK;
}

esp_err_t dev_led_stop_blink_task(void)
{
    if (blink_task_handle == NULL) {
        ESP_LOGW(TAG, "Blink task not running");
        return ESP_OK;
    }

    vTaskDelete(blink_task_handle);
    blink_task_handle = NULL;

    // 停止闪烁后关闭LED
    dev_led_set_state(LED_STATE_OFF);

    ESP_LOGI(TAG, "LED blink task stopped");
    return ESP_OK;
}