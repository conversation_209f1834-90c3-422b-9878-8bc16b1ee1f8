/*
 * SPDX-FileCopyrightText: 2021-2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Unlicense OR CC0-1.0
 */

#ifndef BLE_GATT_H
#define BLE_GATT_H

#include <string.h>
#include <stdlib.h>
#include "ble_gap.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_gatts_api.h"
#include "esp_gatt_common_api.h"
#include "esp_efuse.h"
#include "esp_idf_version.h"
#include "esp_mac.h"

#ifdef __cplusplus
extern "C"
{
#endif

// GATT 相关宏定义
#define PROFILE_NUM 1
#define SLAVE_PROFILE_IDX 0
#define ESP_APP_ID 0x55
#define SVC_INST_ID 0

/* The max length of characteristic value. When the GATT client performs a write or prepare write operation,
 *  the data length must be less than GATTS_DEMO_CHAR_VAL_LEN_MAX.
 */
#define GATTS_DEMO_CHAR_VAL_LEN_MAX 500

#define CHAR_DECLARATION_SIZE (sizeof(uint8_t))

    // 该枚举类型对顺序敏感，
    // 请严格按此顺序来定义枚举值，特征类型与特征值之间没有强关联的，完全按顺序“隐形”关联
    enum
    {
        IDX_SVC,

        // 设备ID, 设备MAC, 电池电量, 温度, 压力
        IDX_CHAR_SLAVE_RESPONSE,
        IDX_CHAR_VAL_SLAVE_RESPONSE,

        HRS_IDX_NB,
    };

    // 应用实例结构体，用于管理每个应用实例的相关信息
    struct gatts_profile_inst
    {
        esp_gatts_cb_t gatts_cb;
        uint16_t gatts_if;
        uint16_t app_id;
        uint16_t conn_id;
        uint16_t service_handle;
        esp_gatt_srvc_id_t service_id;
        uint16_t char_handle;
        esp_bt_uuid_t char_uuid;
        esp_gatt_perm_t perm;
        esp_gatt_char_prop_t property;
        uint16_t descr_handle;
        esp_bt_uuid_t descr_uuid;
    };

    // 减少通讯周期,将所有数据打包一个结构体
    struct gatts_slave_response
    {
        uint16_t device_id;
        uint8_t device_mac[6];
        uint16_t battery_level;
        float temperature;
        float pressure;
    };

    // GATT 相关函数声明
    /**
     * @brief GATT 事件处理函数
     * @param event GATT 事件类型
     * @param gatts_if GATT 接口
     * @param param 事件参数
     */
    void gatts_event_handler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t *param);

    /**
     * @brief 从机配置文件事件处理函数
     * @param event GATT 事件类型
     * @param gatts_if GATT 接口
     * @param param 事件参数
     */
    // 此为"从机设备"应用实例的事件处理函数
    // BLE 中的事件（event）是按"应用实例"分发的：
    // 服务（service）和特征（characteristic）是挂载在某个应用实例下的功能单元
    // 即收到的 gatts_if 表示该事件归属于哪个实例
    // 然后通过 param 中的 handle（句柄）来判断当前操作的是哪个服务或特征
    // 通常做法是：通过 handle 与你定义的服务/特征 handle 做匹配，然后执行对应逻辑
    void gatts_slave_profile_event_handler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t *param);

    /**
     * @brief 初始化 GATT 层
     * @return ESP_OK 成功，其他值表示失败
     */
    esp_err_t ble_gatt_init(void);

    /**
     * @brief 读取温度传感器
     * @return 温度值
     */
    esp_err_t read_temperature_sensor(float *temp);

    /**
     * @brief 读取压力传感器
     * @return 压力值
     */
    esp_err_t read_pressure_sensor(float *press);

#ifdef __cplusplus
}
#endif

#endif /* BLE_GATT_H */
