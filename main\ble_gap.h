/*
 * SPDX-FileCopyrightText: 2021-2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Unlicense OR CC0-1.0
 */

#ifndef BLE_GAP_H
#define BLE_GAP_H

#include "esp_gap_ble_api.h"
#include "esp_bt_defs.h"

#ifdef __cplusplus
extern "C" {
#endif

// GAP 相关宏定义
#define ADV_CONFIG_FLAG (1 << 0)
#define SCAN_RSP_CONFIG_FLAG (1 << 1)
#define SAMPLE_DEVICE_NAME "ESP_GATTS_DEMO"

// GAP 相关函数声明
/**
 * @brief GAP 事件处理函数
 * @param event GAP 事件类型
 * @param param 事件参数
 */
void gap_event_handler(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t *param);

/**
 * @brief 初始化 GAP 层
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t ble_gap_init(void);

/**
 * @brief 开始广播
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t ble_gap_start_advertising(void);

/**
 * @brief 停止广播
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t ble_gap_stop_advertising(void);

/**
 * @brief 设置设备名称
 * @param device_name 设备名称
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t ble_gap_set_device_name(const char *device_name);

/**
 * @brief 配置广播数据
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t ble_gap_config_adv_data(void);

/**
 * @brief 获取广播配置完成状态
 * @return 广播配置完成状态
 */
uint8_t ble_gap_get_adv_config_done(void);

#ifdef __cplusplus
}
#endif

#endif /* BLE_GAP_H */
