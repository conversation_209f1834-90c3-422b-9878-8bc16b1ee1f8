#include "dev_id.h"
#include "sdkconfig.h"
#include "esp_log.h"
#include "esp_efuse.h"
#include "esp_mac.h"
#include "nvs_flash.h"
#include "nvs.h"


static const char *TAG = "ESP_ID";

// 从flash中读取设备配置信息
esp_err_t get_device_id(uint16_t *device_id)
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("device_config", NVS_READONLY, &nvs_handle);
    *device_id = 0xffff; // 设置默认设备ID
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "无法打开NVS存储: %s，使用默认设备ID: %d", esp_err_to_name(err), *device_id);
        return ESP_FAIL;
    }
    // 首次运行时：读取失败后会自动将默认ID写入NVS
    // 后续运行时：直接从NVS读取之前保存的ID
    // 重新烧录后：会重新写入默认ID到NVS
    err = nvs_get_u16(nvs_handle, "device_id", device_id);
    if (err != ESP_OK)
    {
        ESP_LOGW(TAG, "读取设备ID失败: %s，写入默认设备ID: %d", esp_err_to_name(err), *device_id);

        // 首次运行或重新烧录后，写入默认设备ID到NVS
        nvs_close(nvs_handle);
        err = nvs_open("device_config", NVS_READWRITE, &nvs_handle);
        if (err == ESP_OK)
        {
            err = nvs_set_u16(nvs_handle, "device_id", *device_id);
            if (err == ESP_OK)
            {
                err = nvs_commit(nvs_handle);
                ESP_LOGI(TAG, "默认设备ID已保存到NVS: %d", *device_id);
            }
            else
            {
                ESP_LOGE(TAG, "保存设备ID失败: %s", esp_err_to_name(err));
            }
        }
    }
    nvs_close(nvs_handle);
    return ESP_OK; // 总是返回成功，因为我们提供了默认值
}

// ESP32全球唯一ID
esp_err_t get_device_mac(uint8_t *device_mac)
{
    esp_err_t err = esp_efuse_mac_get_default(device_mac);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to get base MAC address: %s", esp_err_to_name(err));
    }
    return err;
}