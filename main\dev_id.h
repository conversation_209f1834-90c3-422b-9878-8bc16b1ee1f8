
#pragma once

#include "sdkconfig.h"
#include "esp_err.h"
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/* MAC地址长度定义 */
#define DEV_MAC_ADDR_LEN 6

/**
 * @brief 初始化设备ID模块
 *
 * 该函数在系统启动时调用一次，读取并缓存设备ID和MAC地址
 *
 * @return
 *     - ESP_OK: 初始化成功
 *     - 其他: 初始化失败的错误码
 */
esp_err_t dev_id_init(void);

/**
 * @brief 获取缓存的设备ID
 *
 * 返回在初始化时读取并缓存的设备ID
 *
 * @param device_id 指向存储设备ID的指针
 * @return
 *     - ESP_OK: 成功获取设备ID
 *     - ESP_ERR_INVALID_STATE: 模块未初始化
 *     - ESP_ERR_INVALID_ARG: 参数无效
 */
esp_err_t get_device_id(uint16_t *device_id);

/**
 * @brief 获取缓存的设备MAC地址
 *
 * 返回在初始化时读取并缓存的MAC地址
 *
 * @param device_mac 指向存储MAC地址的6字节数组
 * @return
 *     - ESP_OK: 成功获取MAC地址
 *     - ESP_ERR_INVALID_STATE: 模块未初始化
 *     - ESP_ERR_INVALID_ARG: 参数无效
 */
esp_err_t get_device_mac(uint8_t *device_mac);

#ifdef __cplusplus
}
#endif
