<table class="fixed" border="0">
    <col width="1000px" /><col width="500px" />
    <tr><td>
        <h2>ESP32 File Server with OTA</h2>
    </td><td>
        <table border="0">
            <tr>
                <td>
                    <label for="newfile">Upload a file</label>
                </td>
                <td colspan="2">
                    <input id="newfile" type="file" onchange="setpath()" style="width:100%;">
                </td>
            </tr>
            <tr>
                <td>
                    <label for="filepath">Set path on server</label>
                </td>
                <td>
                    <input id="filepath" type="text" style="width:100%;">
                </td>
                <td>
                    <button id="upload" type="button" onclick="upload()">Upload</button>
                </td>
            </tr>
        </table>
    </td></tr>
</table>

<hr>

<table class="fixed" border="0">
    <col width="1000px" /><col width="500px" />
    <tr><td>
        <h3>Firmware Update (OTA)</h3>
        <p style="color: red;"><strong>Warning:</strong> Only upload valid ESP32 firmware (.bin) files!</p>
    </td><td>
        <table border="0">
            <tr>
                <td>
                    <label for="firmwarefile">Select firmware file (.bin)</label>
                </td>
                <td colspan="2">
                    <input id="firmwarefile" type="file" accept=".bin" style="width:100%;">
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <button id="otaupload" type="button" onclick="uploadFirmware()" style="background-color: #ff6b6b; color: white; font-weight: bold;">Update Firmware</button>
                </td>
            </tr>
        </table>
    </td></tr>
</table>
<script>
function setpath() {
    var default_path = document.getElementById("newfile").files[0].name;
    document.getElementById("filepath").value = default_path;
}
function upload() {
    var filePath = document.getElementById("filepath").value;
    var upload_path = "/upload/" + filePath;
    var fileInput = document.getElementById("newfile").files;

    /* Max size of an individual file. Make sure this
     * value is same as that set in file_server.c */
    var MAX_FILE_SIZE = 512*1024;
    var MAX_FILE_SIZE_STR = "512KB";

    if (fileInput.length == 0) {
        alert("No file selected!");
    } else if (filePath.length == 0) {
        alert("File path on server is not set!");
    } else if (filePath.indexOf(' ') >= 0) {
        alert("File path on server cannot have spaces!");
    } else if (filePath[filePath.length-1] == '/') {
        alert("File name not specified after path!");
    } else if (fileInput[0].size > MAX_FILE_SIZE) {
        alert("File size must be less than " + MAX_FILE_SIZE_STR + "!");
    } else {
        document.getElementById("newfile").disabled = true;
        document.getElementById("filepath").disabled = true;
        document.getElementById("upload").disabled = true;

        var file = fileInput[0];
        var xhttp = new XMLHttpRequest();
        xhttp.onreadystatechange = function() {
            if (xhttp.readyState == 4) {
                if (xhttp.status == 200) {
                    document.open();
                    document.write(xhttp.responseText);
                    document.close();
                } else if (xhttp.status == 0) {
                    alert("Server closed the connection abruptly!");
                    location.reload()
                } else {
                    alert(xhttp.status + " Error!\n" + xhttp.responseText);
                    location.reload()
                }
            }
        };
        xhttp.open("POST", upload_path, true);
        xhttp.send(file);
    }
}

function uploadFirmware() {
    var fileInput = document.getElementById("firmwarefile").files;

    if (fileInput.length == 0) {
        alert("No firmware file selected!");
        return;
    }

    var file = fileInput[0];

    // Check if file has .bin extension
    if (!file.name.toLowerCase().endsWith('.bin')) {
        alert("Please select a valid firmware file with .bin extension!");
        return;
    }

    // Check firmware file size (2MB limit)
    if (file.size > 2*1024*1024) {
        alert("Firmware file size must be less than 2MB!");
        return;
    }

    // Confirm firmware update
    if (!confirm("Are you sure you want to update the firmware? This will restart the device!")) {
        return;
    }

    document.getElementById("firmwarefile").disabled = true;
    document.getElementById("otaupload").disabled = true;
    document.getElementById("otaupload").innerHTML = "Updating...";

    var xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {
        if (xhttp.readyState == 4) {
            if (xhttp.status == 200) {
                alert("Firmware update successful! Device will restart.");
                document.open();
                document.write(xhttp.responseText);
                document.close();
            } else if (xhttp.status == 0) {
                alert("Device is restarting after firmware update...");
                setTimeout(function() {
                    location.reload();
                }, 10000);
            } else {
                alert("Firmware update failed: " + xhttp.status + " - " + xhttp.responseText);
                document.getElementById("firmwarefile").disabled = false;
                document.getElementById("otaupload").disabled = false;
                document.getElementById("otaupload").innerHTML = "Update Firmware";
            }
        }
    };

    xhttp.open("POST", "/ota", true);
    xhttp.send(file);
}
</script>
