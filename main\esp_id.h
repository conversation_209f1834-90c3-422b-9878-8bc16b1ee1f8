
#pragma once

#include "sdkconfig.h"
#include "esp_err.h"
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 从NVS存储中读取设备ID
 *
 * 该函数从NVS存储中读取设备ID。如果读取失败，会使用默认值0xFFFF
 * 并将默认值写入NVS存储以供后续使用。
 *
 * @param device_id 指向存储设备ID的指针
 * @return
 *     - ESP_OK: 成功读取设备ID
 *     - ESP_FAIL: 读取失败，但已设置默认值
 */
esp_err_t get_device_id(uint16_t *device_id);

/**
 * @brief 读取ESP32的MAC地址
 *
 * 该函数读取ESP32芯片的全球唯一MAC地址（6字节）
 *
 * @param device_mac 指向存储MAC地址的6字节数组
 * @return
 *     - ESP_OK: 成功读取MAC地址
 *     - 其他: 读取失败的错误码
 */
esp_err_t get_read_device_mac(uint8_t *device_mac);

#ifdef __cplusplus
}
#endif
