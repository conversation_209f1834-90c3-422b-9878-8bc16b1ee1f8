/*
 * WiFi AP Mode Configuration for ESP32 File Server with OTA
 */

#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "esp_netif.h"
#include "esp_mac.h"

#include "lwip/err.h"
#include "lwip/sys.h"
#include "dev_id.h"

static const char *TAG = "wifi_ap";
static esp_netif_t *wifi_netif = NULL;
static esp_event_handler_instance_t instance_any_id = NULL;
static bool wifi_initialized = false;

static uint8_t wifi_ap_ssid[32] = "TPM_JAR_FF";
static uint8_t wifi_ap_pass[64] = "12345678";
static uint8_t wifi_ap_channel = 1;
static uint8_t wifi_ap_max_conn = 4;

static void wifi_event_handler(void* arg, esp_event_base_t event_base,int32_t event_id, void* event_data)
{
    if (event_id == WIFI_EVENT_AP_STACONNECTED) {
        wifi_event_ap_staconnected_t* event = (wifi_event_ap_staconnected_t*) event_data;
        ESP_LOGI(TAG, "Station %02x:%02x:%02x:%02x:%02x:%02x joined, AID=%d",
                 event->mac[0], event->mac[1], event->mac[2],
                 event->mac[3], event->mac[4], event->mac[5], event->aid);
    } 
    else if (event_id == WIFI_EVENT_AP_STADISCONNECTED) {
        wifi_event_ap_stadisconnected_t* event = (wifi_event_ap_stadisconnected_t*) event_data;
        ESP_LOGI(TAG, "Station %02x:%02x:%02x:%02x:%02x:%02x left, AID=%d",
                 event->mac[0], event->mac[1], event->mac[2],
                 event->mac[3], event->mac[4], event->mac[5], event->aid);
    }
}

esp_err_t wifi_init_ap(void)
{
    esp_err_t ret;

    // 检查是否已经初始化
    if (wifi_initialized) {
        ESP_LOGW(TAG, "WiFi AP already initialized");
        return ESP_OK;
    }

    // 检查是否已经初始化，避免重复初始化
    static bool netif_initialized = false;
    static bool event_loop_initialized = false;

    if (!netif_initialized) {
        ret = esp_netif_init();
        if (ret == ESP_OK) {
            netif_initialized = true;
        } 
        else if (ret != ESP_ERR_INVALID_STATE) {
            ESP_LOGE(TAG, "Failed to initialize netif: %s", esp_err_to_name(ret));
            return ret;
        }
    }

    if (!event_loop_initialized) {
        ret = esp_event_loop_create_default();
        if (ret == ESP_OK) {
            event_loop_initialized = true;
        } 
        else if (ret != ESP_ERR_INVALID_STATE) {
            ESP_LOGE(TAG, "Failed to create event loop: %s", esp_err_to_name(ret));
            return ret;
        }
    }

    // 创建WiFi AP网络接口（只创建一次）
    if (wifi_netif == NULL) {
        wifi_netif = esp_netif_create_default_wifi_ap();
        if (wifi_netif == NULL) {
            ESP_LOGE(TAG, "Failed to create WiFi AP netif");
            return ESP_FAIL;
        }
    }

    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ret = esp_wifi_init(&cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize WiFi: %s", esp_err_to_name(ret));
        return ret;
    }

    // 注册事件处理器（只注册一次）
    if (instance_any_id == NULL) {
        ret = esp_event_handler_instance_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, NULL, &instance_any_id);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to register WiFi event handler: %s", esp_err_to_name(ret));
            esp_wifi_deinit();
            return ret;
        }
    }

    // 在wifi_ap_ssid中显示设备ID供用户区分
    uint16_t device_id;
    uint8_t device_mac[6];
    get_device_id(&device_id);
    get_device_mac(device_mac);
    sprintf(wifi_ap_ssid, "TPM_Jar%", device_id);

    wifi_config_t wifi_config = {
        .ap = {
            .ssid = wifi_ap_ssid,
            .ssid_len = strlen(wifi_ap_ssid),
            .channel = wifi_ap_channel,
            .password = wifi_ap_pass,
            .max_connection = wifi_ap_max_conn,
            .authmode = WIFI_AUTH_WPA2_PSK,
            .pmf_cfg = {
                .required = false,
            },
        },
    };
    
    if (strlen(wifi_ap_pass) == 0) {
        wifi_config.ap.authmode = WIFI_AUTH_OPEN;
    }

    ret = esp_wifi_set_mode(WIFI_MODE_AP);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set WiFi mode: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = esp_wifi_set_config(WIFI_IF_AP, &wifi_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set WiFi config: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = esp_wifi_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start WiFi: %s", esp_err_to_name(ret));
        return ret;
    }

    wifi_initialized = true;
    ESP_LOGI(TAG, "WiFi AP started. SSID:%s password:%s channel:%d",
             wifi_ap_ssid, wifi_ap_pass, wifi_ap_channel);

    return ESP_OK;
}

esp_err_t wifi_deinit_ap(void)
{
    esp_err_t ret;

    if (!wifi_initialized) {
        ESP_LOGW(TAG, "WiFi AP not initialized");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Stopping WiFi AP...");

    // Stop WiFi
    ret = esp_wifi_stop();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop WiFi: %s", esp_err_to_name(ret));
        // Continue with cleanup even if stop fails
    }

    // Unregister event handler
    if (instance_any_id != NULL) {
        ret = esp_event_handler_instance_unregister(WIFI_EVENT, ESP_EVENT_ANY_ID, instance_any_id);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to unregister WiFi event handler: %s", esp_err_to_name(ret));
        }
        instance_any_id = NULL;
    }

    // Deinitialize WiFi
    ret = esp_wifi_deinit();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to deinitialize WiFi: %s", esp_err_to_name(ret));
        // Continue with cleanup even if deinit fails
    }

    // Destroy WiFi netif
    if (wifi_netif != NULL) {
        esp_netif_destroy_default_wifi(wifi_netif);
        wifi_netif = NULL;
    }

    wifi_initialized = false;
    ESP_LOGI(TAG, "WiFi AP stopped successfully");
    return ESP_OK;
}
